import { lazy } from "react";
import {
  createBrowserRouter,
  createRoutesFromElements,
  Outlet,
  Route,
} from "react-router-dom";
import { ProtectedAdminRoute } from "./components/authorization/authAdminGuard";
import { ProtectedRoute } from "./components/authorization/authGuard";
import { ProtectedLoginRoute } from "./components/authorization/authLoginGuard";
import { AuthSettingsGuard } from "./components/authorization/authSettingsGuard";
import { StoreGuard } from "./components/authorization/storeGuard";
import Layout from "./components/main/layout";
import KnowledgeWrapper from "./components/main/layout/components/knowledgeWrapper";
import ContextWrapper from "./contexts";
import { CustomNavigate } from "./customization/components/custom-navigate";
import { BASENAME } from "./customization/config-constants";
import {
  ENABLE_CUSTOM_PARAM,
  ENABLE_FILE_MANAGEMENT,
} from "./customization/feature-flags";
import { AppAuthenticatedPage } from "./pages/AppAuthenticatedPage";
import { AppInitPage } from "./pages/AppInitPage";
import { AppWrapperPage } from "./pages/AppWrapperPage";
import { DashboardWrapperPage } from "./pages/DashboardWrapperPage";

/** 以下几个页面不使用懒加载，以提升页面加载速度 */
import AppPage from "./pages/AppPage";
import Knowledge from "./pages/Knowledge";
import Workflow from "./pages/Workflow";
import UserSettingModel from "./ragflow/pages/user-setting/setting-model";

/** 应用管理 */
const AppConfig = lazy(() => import("./pages/AppPage/AppConfig"));
const AppChat = lazy(() => import("./pages/AppPage/AppChat"));
const AppPublish = lazy(() => import("./pages/AppPage/AppPublish"));

/** 工作流 */
const FlowPage = lazy(() => import("./pages/FlowPage"));

/** 知识库 */
const Doc = lazy(() => import("./pages/Knowledge/Doc"));
const KnowledgeTesting = lazy(
  () => import("./ragflow/pages/add-knowledge/components/knowledge-testing"),
);
const KnowledgeSetting = lazy(
  () => import("./ragflow/pages/add-knowledge/components/knowledge-setting"),
);
const DocumentDetail = lazy(
  () => import("./ragflow/pages/add-knowledge/components/knowledge-chunk"),
);

/** 登录 */
const LoginPage = lazy(() => import("./pages/LoginPage"));

/** 项目中原有的页面（TODO：部分不需要的页面后续需要移除） */
const AdminPage = lazy(() => import("./pages/AdminPage"));
const LoginAdminPage = lazy(() => import("./pages/AdminPage/LoginPage"));
const DeleteAccountPage = lazy(() => import("./pages/DeleteAccountPage"));
const PlaygroundPage = lazy(() => import("./pages/Playground"));
const SignUp = lazy(() => import("./pages/SignUpPage"));
const SettingsPage = lazy(() => import("./pages/SettingsPage"));
const StorePage = lazy(() => import("./pages/StorePage"));
const CollectionPage = lazy(() => import("./pages/MainPage/pages"));
const FilesPage = lazy(() => import("./pages/MainPage/pages/filesPage"));
const HomePage = lazy(() => import("./pages/MainPage/pages/homePage"));
const ViewPage = lazy(() => import("./pages/ViewPage"));
const GeneralPage = lazy(
  () => import("./pages/SettingsPage/pages/GeneralPage"),
);
const ApiKeysPage = lazy(
  () => import("./pages/SettingsPage/pages/ApiKeysPage"),
);
const GlobalVariablesPage = lazy(
  () => import("./pages/SettingsPage/pages/GlobalVariablesPage"),
);
const MessagesPage = lazy(
  () => import("./pages/SettingsPage/pages/messagesPage"),
);
const ShortcutsPage = lazy(
  () => import("./pages/SettingsPage/pages/ShortcutsPage"),
);
const StoreApiKeyPage = lazy(
  () => import("./pages/SettingsPage/pages/StoreApiKeyPage"),
);

const router = createBrowserRouter(
  createRoutesFromElements([
    <Route key="/playground/:id/" path="/playground/:id/">
      <Route
        path=""
        element={
          <ContextWrapper key={1}>
            <PlaygroundPage />
          </ContextWrapper>
        }
      />
    </Route>,
    <Route
      key="/"
      path={ENABLE_CUSTOM_PARAM ? "/:customParam?" : "/"}
      element={
        <ContextWrapper key={2}>
          <Outlet />
        </ContextWrapper>
      }
    >
      <Route path="" element={<AppInitPage />}>
        <Route path="" element={<AppWrapperPage />}>
          <Route
            path=""
            element={
              <ProtectedRoute>
                <Outlet />
              </ProtectedRoute>
            }
          >
            <Route path="/" element={<Layout />}>
              <Route
                index
                element={<CustomNavigate replace to={"workflow"} />}
              />
              <Route path="app">
                <Route index element={<AppPage />} />
                <Route path=":appId">
                  <Route path="config" element={<AppConfig />} />
                  <Route path="publish" element={<AppPublish />} />
                </Route>
              </Route>
              <Route path="workflow" element={<Workflow />} />
              <Route path="knowledge">
                <Route index element={<Knowledge />}></Route>
                <Route path=":knowledgeId" element={<KnowledgeWrapper />}>
                  <Route path="testing" element={<KnowledgeTesting />} />
                  <Route path="document">
                    <Route index element={<Doc />}></Route>
                    <Route path=":documentId" element={<DocumentDetail />} />
                  </Route>
                  <Route path="setting" element={<KnowledgeSetting />} />
                </Route>
              </Route>
              <Route path="model" element={<UserSettingModel />} />
            </Route>
            <Route path="/chat/:appId" element={<AppChat />} />
            <Route path="" element={<AppAuthenticatedPage />}>
              <Route path="" element={<DashboardWrapperPage />}>
                <Route path="" element={<CollectionPage />}>
                  <Route
                    index
                    element={<CustomNavigate replace to={"flows"} />}
                  />
                  {ENABLE_FILE_MANAGEMENT && (
                    <Route path="files" element={<FilesPage />} />
                  )}
                  <Route
                    path="flows/"
                    element={<HomePage key="flows" type="flows" />}
                  >
                    <Route
                      path="folder/:folderId"
                      element={<HomePage key="flows" type="flows" />}
                    />
                  </Route>
                  <Route
                    path="components/"
                    element={<HomePage key="components" type="components" />}
                  >
                    <Route
                      path="folder/:folderId"
                      element={<HomePage key="components" type="components" />}
                    />
                  </Route>
                  <Route
                    path="all/"
                    element={<HomePage key="flows" type="flows" />}
                  >
                    <Route
                      path="folder/:folderId"
                      element={<HomePage key="flows" type="flows" />}
                    />
                  </Route>
                </Route>
                <Route path="settings" element={<SettingsPage />}>
                  <Route
                    index
                    element={<CustomNavigate replace to={"general"} />}
                  />
                  <Route
                    path="global-variables"
                    element={<GlobalVariablesPage />}
                  />
                  <Route path="api-keys" element={<ApiKeysPage />} />
                  <Route
                    path="general/:scrollId?"
                    element={
                      <AuthSettingsGuard>
                        <GeneralPage />
                      </AuthSettingsGuard>
                    }
                  />
                  <Route path="shortcuts" element={<ShortcutsPage />} />
                  <Route path="messages" element={<MessagesPage />} />
                  <Route path="store" element={<StoreApiKeyPage />} />
                </Route>
                <Route
                  path="store"
                  element={
                    <StoreGuard>
                      <StorePage />
                    </StoreGuard>
                  }
                />
                <Route
                  path="store/:id/"
                  element={
                    <StoreGuard>
                      <StorePage />
                    </StoreGuard>
                  }
                />
                <Route path="account">
                  <Route path="delete" element={<DeleteAccountPage />}></Route>
                </Route>
                <Route
                  path="admin"
                  element={
                    <ProtectedAdminRoute>
                      <AdminPage />
                    </ProtectedAdminRoute>
                  }
                />
              </Route>
              <Route path="flow/:id/">
                <Route path="" element={<DashboardWrapperPage />}>
                  <Route path="folder/:folderId/" element={<FlowPage />} />
                  <Route path="" element={<FlowPage />} />
                </Route>
                <Route path="view" element={<ViewPage />} />
              </Route>
            </Route>
          </Route>
          <Route
            path="login"
            element={
              <ProtectedLoginRoute>
                <LoginPage />
              </ProtectedLoginRoute>
            }
          />
          <Route
            path="signup"
            element={
              <ProtectedLoginRoute>
                <SignUp />
              </ProtectedLoginRoute>
            }
          />
          <Route
            path="login/admin"
            element={
              <ProtectedLoginRoute>
                <LoginAdminPage />
              </ProtectedLoginRoute>
            }
          />
        </Route>
      </Route>
      <Route path="*" element={<CustomNavigate replace to="/" />} />
    </Route>,
  ]),
  { basename: BASENAME || undefined },
);

export default router;
