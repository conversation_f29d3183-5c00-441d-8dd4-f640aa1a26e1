import { Form } from "antd";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

import SubPageLayout from "@/components/main/layout/components/subPageLayout";
import { useGetAppDetail } from "@/controllers/API/queries/app/useGetAppDetail";
import { useUpdateApp } from "@/controllers/API/queries/app/useUpdateApp";
import { useGetAppChatLink } from "@/hooks/app/useGetAppChatLink";
import { useAlert } from "@/hooks/useAlert";
import { AppConfigFormData } from "@/types/app";
import AppConfigPanel from "../components/appConfigPanel";
import ChatPreview from "../components/chatPreview";
import Toolbar from "../components/toolbar";
import { transformToAppData, transformToFormData } from "../utils";

export default function AppConfig() {
  const [data, setData] = useState<AppConfigFormData | undefined>();
  const [hasSaved, setHasSaved] = useState(true);

  const [form] = Form.useForm();
  const { appId = "" } = useParams();
  const alert = useAlert();
  const chatLink = useGetAppChatLink();

  const { data: appDetail } = useGetAppDetail({ appId });
  const { mutate: updateApp } = useUpdateApp();

  useEffect(() => {
    if (appDetail) {
      setData((prev) => ({ ...prev, ...transformToFormData(appDetail) }));
    }
  }, [appDetail]);

  const handleValuesChange = (
    values: AppConfigFormData,
    immediateUpdate = false,
    isEffectSaveStatus = true,
  ) => {
    if (immediateUpdate) {
      setData((prev) => ({ ...prev, ...values }));
    }
    if (isEffectSaveStatus) {
      setHasSaved(false);
    }
  };

  const handleRun = () => {
    window.open(chatLink, "_blank");
  };

  const handleSave = (isPublish?: boolean) => {
    const formData = form.getFieldsValue(true);
    const params = transformToAppData(formData);

    updateApp(
      { ...params, app_id: appId },
      {
        onSuccess: () => {
          alert.success(isPublish ? "保存并发布成功" : "保存成功");
          setData(formData);
          setHasSaved(true);
        },
      },
    );
  };

  const getFormData = () => {
    return form.getFieldsValue(true);
  };

  return (
    <SubPageLayout
      title="应用编排"
      backPath="/app"
      navRightContent={
        <Toolbar
          appId={appId}
          updateTime={appDetail?.updated_at || ""}
          hasSaved={hasSaved}
          onRun={handleRun}
          onSave={handleSave}
        />
      }
    >
      <div className="flex h-full rounded-lg border border-solid border-border-1">
        <AppConfigPanel
          form={form}
          data={data}
          onValuesChange={handleValuesChange}
        />
        <ChatPreview data={data} getCompleteData={getFormData} isDebugger />
      </div>
    </SubPageLayout>
  );
}
