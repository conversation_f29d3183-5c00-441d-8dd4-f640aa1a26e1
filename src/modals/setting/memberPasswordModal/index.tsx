import { Button, Form, Input, Modal } from "antd";

import Copy from "@/components/antd/copy";
import { CopyIcon } from "@/components/main/icon";
import { useUpdatePassword } from "@/controllers/API/queries/tenant/useUpdatePassword";

interface MemberPasswordModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (value: string) => void;
  userId: string;
}

export default function MemberPasswordModal({
  open,
  onCancel,
  onOk,
  userId,
}: MemberPasswordModalProps) {
  const [form] = Form.useForm();
  const password = Form.useWatch("password", form);

  const { mutateAsync: updatePassword, isPending } = useUpdatePassword();

  const generatePassword = () => {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let password = "";
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    form.setFieldsValue({ password });
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      await updatePassword({
        user_id: userId,
        new_password: values.password,
      });
      onOk(values.password);
      form.resetFields();
    } catch (error) {
      console.log("表单验证失败:", error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="修改密码"
      open={open}
      width={400}
      className="add-member-modal"
      getContainer={false}
      okText="确认修改"
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={isPending}
    >
      <Form form={form} layout="vertical" className="mt-6">
        <Form.Item label="新密码" className="mb-0" required>
          <div className="flex gap-2">
            <Form.Item
              name="password"
              rules={[
                { required: true, message: "请输入新密码" },
                { min: 6, message: "密码长度至少6位" },
              ]}
              className="flex-1"
            >
              <Input
                placeholder="请输入新密码"
                suffix={
                  <Copy
                    button={
                      <CopyIcon className="cursor-pointer text-sm text-text-2" />
                    }
                    text={password}
                  />
                }
              />
            </Form.Item>
            <Button onClick={generatePassword}>生成</Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
}
