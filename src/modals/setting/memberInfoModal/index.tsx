import { Button, Modal } from "antd";

import Copy from "@/components/antd/copy";
import { CircleCheckFilledIcon } from "@/components/main/icon";
import type { MemberFormData } from "@/types/member";

interface IMemberInfoModal {
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
  data?: MemberFormData;
}

export default function MemberInfoModal({
  open,
  onCancel,
  data,
}: IMemberInfoModal) {
  const infoList = [
    { label: "用户名", value: data?.username },
    { label: "邮箱", value: data?.email },
    { label: "密码", value: data?.password },
    { label: "登录地址", value: `${window.location.origin}/login` },
  ];

  const copyText = infoList
    .map((item) => `${item.label}\n${item.value}`)
    .join("\n");

  const handleCancel = () => {
    onCancel();
  };

  return (
    <Modal
      title="成员创建成功"
      open={open}
      width={400}
      getContainer={false}
      onCancel={handleCancel}
      footer={() => (
        // FIXME: 这里复制有问题需要排查一下
        <Copy
          button={<Button type="primary">复制登录信息</Button>}
          text={copyText}
        />
      )}
    >
      <div className="my-6 flex gap-2 rounded-lg border border-[#11D7B2] bg-[#E7FBF7] p-3 text-sm">
        <CircleCheckFilledIcon className="text-[20px] text-[#11D7B2]" />
        <div>
          成员
          <span className="mx-1 font-semibold">{data?.username}</span>
          已创建成功
        </div>
      </div>
      <div className="mb-2 text-text-2">请将以下登录信息发送给新成员：</div>
      <div className="mb-6 flex flex-col gap-4 rounded-lg border border-border-1 p-3">
        {infoList.map((item) => (
          <div key={item.label}>
            <div className="mb-1 font-medium">{item.label}</div>
            <div className="text-text-2">{item.value}</div>
          </div>
        ))}
      </div>
    </Modal>
  );
}
