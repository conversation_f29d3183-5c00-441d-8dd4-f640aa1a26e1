import { EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
import { Button, Form, Input, Modal, Select } from "antd";
import { useEffect } from "react";

import type { MemberFormData, MemberListItem } from "@/types/member";

export interface AddMemberModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (values: MemberFormData) => void;
  data?: MemberListItem;
}

export default function AddMemberModal({
  open,
  onCancel,
  onOk,
  data,
}: AddMemberModalProps) {
  const [form] = Form.useForm();

  const isEdit = !!data;
  const departmentOptions = [
    { label: "技术研发部", value: "技术研发部" },
    { label: "产品部", value: "产品部" },
    { label: "市场部", value: "市场部" },
    { label: "人事部", value: "人事部" },
  ];
  const roleOptions = [
    { label: "成员", value: "成员" },
    { label: "管理员", value: "管理员" },
    { label: "所有者", value: "所有者" },
  ];

  useEffect(() => {
    if (!open) return;
    if (data) {
      form.setFieldsValue({
        username: data.username,
        email: data.email,
        department: data.department,
        role: data.role,
      });
    } else {
      form.resetFields();
    }
  }, [open, data, form]);

  const generatePassword = () => {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let password = "";
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    console.log("password:", password);
    form.setFieldsValue({ password });
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onOk(values);
      form.resetFields();
    } catch (error) {
      console.log("表单验证失败:", error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={isEdit ? "编辑成员" : "添加成员"}
      open={open}
      width={400}
      className="add-member-modal"
      getContainer={false}
      okText="创建成员"
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <Form form={form} layout="vertical" className="mt-6">
        <Form.Item
          name="username"
          label="用户名"
          rules={[{ required: true, message: "请输入用户名" }]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>
        <Form.Item
          name="email"
          label="邮箱"
          rules={[
            { required: true, message: "请输入邮箱" },
            { type: "email", message: "请输入有效的邮箱地址" },
          ]}
        >
          <Input placeholder="请输入邮箱" />
        </Form.Item>
        <Form.Item
          name="phone"
          label="手机号"
          rules={[
            { pattern: /^1[3-9]\d{9}$/, message: "请输入有效的手机号码" },
          ]}
        >
          <Input placeholder="请输入手机号" />
        </Form.Item>
        <Form.Item
          name="department"
          label="部门"
          rules={[{ required: true, message: "请选择部门" }]}
        >
          <Select placeholder="请选择部门" options={departmentOptions} />
        </Form.Item>
        <Form.Item
          name="role"
          label="角色"
          rules={[{ required: true, message: "请选择角色" }]}
        >
          <Select
            placeholder="请选择角色"
            options={roleOptions}
            disabled={isEdit}
          />
        </Form.Item>
        {!isEdit && (
          <Form.Item label="初始密码" className="mb-0" required>
            <div className="flex gap-2">
              <Form.Item
                name="password"
                rules={[
                  { required: true, message: "请输入初始密码" },
                  { min: 6, message: "密码长度至少6位" },
                ]}
                className="flex-1"
              >
                <Input.Password
                  placeholder="请输入初始密码"
                  iconRender={(visible) =>
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                />
              </Form.Item>
              <Button onClick={generatePassword}>生成</Button>
            </div>
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
}
