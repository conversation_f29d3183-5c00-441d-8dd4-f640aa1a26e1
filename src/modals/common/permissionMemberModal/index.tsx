import { UserOutlined } from "@ant-design/icons";
import {
  Avatar,
  Breadcrumb,
  Button,
  Checkbox,
  Input,
  Modal,
  Select,
} from "antd";
import { useCallback, useMemo, useState } from "react";

import EmptyIcon from "@/assets/Empty.svg?react";
import {
  RightArrowIcon,
  SearchIcon,
  TeamIcon,
  UserGroupIcon,
} from "@/components/main/icon";
import { useGetOrganizationMembers } from "@/controllers/API/queries/organization/useGetOrganizationMembers";
import { cn } from "@/utils/utils";
import {
  PERMISSION_TYPE_OPTIONS,
  PermissionType,
} from "../resourcePermissionModal/helper";

// 组织架构节点类型
interface OrganizationNode {
  id: string;
  name: string;
  type: "organization" | "user";
  avatar?: string;
  children?: OrganizationNode[];
  parentId?: string;
}

// 已选择的成员或组织
interface SelectedMember {
  id: string;
  name: string;
  type: "organization" | "user";
  avatar?: string;
  permission: PermissionType;
  isReadonly?: boolean; // 外部传入的不可编辑
}

interface PermissionMemberModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (selectedMembers: SelectedMember[]) => void;
  initialSelectedMembers?: SelectedMember[]; // 外部已选择的成员
}

// 模拟组织架构数据
const mockOrganizationData: OrganizationNode = {
  id: "root",
  name: "AIWorks 团队",
  type: "organization",
  children: [
    {
      id: "tech",
      name: "技术部",
      type: "organization",
      parentId: "root",
      children: [
        {
          id: "frontend",
          name: "前端小组",
          type: "organization",
          parentId: "tech",
          children: [
            { id: "user1", name: "张三", type: "user", parentId: "frontend" },
            { id: "user2", name: "李四", type: "user", parentId: "frontend" },
            { id: "user3", name: "王五", type: "user", parentId: "frontend" },
            { id: "user6", name: "周八", type: "user", parentId: "frontend" },
            { id: "user7", name: "周九", type: "user", parentId: "frontend" },
            { id: "user8", name: "周十", type: "user", parentId: "frontend" },
            { id: "user9", name: "周十一", type: "user", parentId: "frontend" },
            {
              id: "user10",
              name: "周十二",
              type: "user",
              parentId: "frontend",
            },
            {
              id: "user11",
              name: "周十三",
              type: "user",
              parentId: "frontend",
            },
            {
              id: "user12",
              name: "周十四",
              type: "user",
              parentId: "frontend",
            },
            {
              id: "user13",
              name: "周十五",
              type: "user",
              parentId: "frontend",
            },
            {
              id: "user14",
              name: "周十六",
              type: "user",
              parentId: "frontend",
            },
            {
              id: "user15",
              name: "周十七",
              type: "user",
              parentId: "frontend",
            },
            {
              id: "user16",
              name: "周十八",
              type: "user",
              parentId: "frontend",
            },
            {
              id: "user17",
              name: "周十九",
              type: "user",
              parentId: "frontend",
            },
            {
              id: "user18",
              name: "周二十",
              type: "user",
              parentId: "frontend",
            },
          ],
        },
        {
          id: "backend",
          name: "后端小组",
          type: "organization",
          parentId: "tech",
          children: [
            { id: "user4", name: "赵六", type: "user", parentId: "backend" },
          ],
        },
        {
          id: "test",
          name: "测试小组",
          type: "organization",
          parentId: "tech",
          children: [
            { id: "user5", name: "孙七", type: "user", parentId: "test" },
          ],
        },
      ],
    },
  ],
};

export default function PermissionMemberModal({
  open,
  onClose,
  onConfirm,
  initialSelectedMembers = [],
}: PermissionMemberModalProps) {
  const [searchValue, setSearchValue] = useState("");
  const [currentPath, setCurrentPath] = useState<string[]>(["root"]);
  const [selectedMembers, setSelectedMembers] = useState<SelectedMember[]>(
    initialSelectedMembers,
  );
  const [checkedNodes, setCheckedNodes] = useState<Set<string>>(
    new Set(initialSelectedMembers.map((m) => m.id)),
  );
  const [currentOrganizationId, setCurrentOrganizationId] = useState<
    string | undefined
  >();

  const { data: organizationMembers } = useGetOrganizationMembers(
    { org_id: currentOrganizationId },
    { enabled: open },
  );

  // 获取当前路径对应的节点
  const getCurrentNode = useCallback(
    (path: string[]): OrganizationNode | null => {
      let current = mockOrganizationData;

      for (let i = 1; i < path.length; i++) {
        const nodeId = path[i];
        const found = current.children?.find((child) => child.id === nodeId);
        if (!found) return null;
        current = found;
      }
      return current;
    },
    [],
  );

  // 搜索功能
  const searchNodes = useCallback(
    (node: OrganizationNode, query: string): OrganizationNode[] => {
      const results: OrganizationNode[] = [];

      if (node.name.toLowerCase().includes(query.toLowerCase())) {
        results.push(node);
      }
      if (node.children) {
        for (const child of node.children) {
          results.push(...searchNodes(child, query));
        }
      }
      return results;
    },
    [],
  );

  // 获取显示的节点列表
  const displayNodes = useMemo(() => {
    if (searchValue.trim()) {
      // 搜索模式：显示搜索结果
      return searchNodes(mockOrganizationData, searchValue.trim());
    } else {
      // 正常模式：显示当前路径下的子节点
      const currentNode = getCurrentNode(currentPath);
      return currentNode?.children || [];
    }
  }, [searchValue, currentPath, getCurrentNode, searchNodes]);

  // 面包屑导航数据
  const breadcrumbItems = useMemo(() => {
    const items: { id: string; name: string }[] = [];
    let current = mockOrganizationData;

    items.push({ id: current.id, name: current.name });

    for (let i = 1; i < currentPath.length; i++) {
      const nodeId = currentPath[i];
      const found = current.children?.find((child) => child.id === nodeId);
      if (found) {
        items.push({ id: found.id, name: found.name });
        current = found;
      }
    }
    if (searchValue.trim()) {
      return [items[0]];
    }
    return items;
  }, [currentPath, searchValue]);

  // 处理节点点击（进入子组织）
  const handleNodeClick = useCallback((node: OrganizationNode) => {
    if (
      node.type === "organization" &&
      node.children &&
      node.children.length > 0
    ) {
      setCurrentPath((prev) => [...prev, node.id]);
      setSearchValue(""); // 清空搜索
    }
  }, []);

  // 处理面包屑点击
  const handleBreadcrumbClick = useCallback(
    (targetId: string) => {
      const targetIndex = currentPath.findIndex((id) => id === targetId);
      if (targetIndex !== -1) {
        setCurrentPath(currentPath.slice(0, targetIndex + 1));
        setSearchValue(""); // 清空搜索
      }
    },
    [currentPath],
  );

  // 检查节点是否被选中
  const isNodeChecked = useCallback(
    (nodeId: string) => {
      return checkedNodes.has(nodeId);
    },
    [checkedNodes],
  );

  // 检查节点是否不可编辑（父组织被选中）
  const isNodeDisabled = useCallback(
    (nodeId: string) => {
      // 如果是外部传入的只读成员，不可编辑
      const initialMember = initialSelectedMembers.find((m) => m.id === nodeId);
      if (initialMember?.isReadonly) return true;

      // 检查父组织是否被选中
      const findParentPath = (
        node: OrganizationNode,
        targetId: string,
        path: string[] = [],
      ): string[] | null => {
        if (node.id === targetId) return path;

        if (node.children) {
          for (const child of node.children) {
            const result = findParentPath(child, targetId, [...path, node.id]);
            if (result) return result;
          }
        }
        return null;
      };

      const parentPath = findParentPath(mockOrganizationData, nodeId);
      if (parentPath) {
        return parentPath.some((parentId) => checkedNodes.has(parentId));
      }
      return false;
    },
    [checkedNodes, initialSelectedMembers],
  );

  // 处理节点勾选
  const handleNodeCheck = useCallback(
    (node: OrganizationNode, checked: boolean) => {
      if (isNodeDisabled(node.id)) return;

      const newCheckedNodes = new Set(checkedNodes);
      const newSelectedMembers = [...selectedMembers];

      if (checked) {
        // 选中节点
        newCheckedNodes.add(node.id);

        // 添加到已选择列表
        if (!selectedMembers.find((m) => m.id === node.id)) {
          newSelectedMembers.push({
            id: node.id,
            name: node.name,
            type: node.type,
            avatar: node.avatar,
            permission: PermissionType.EDITABLE,
            isReadonly: false,
          });
        }

        // 如果选中的是组织，取消选中其子节点
        if (node.type === "organization" && node.children) {
          const removeChildNodes = (children: OrganizationNode[]) => {
            children.forEach((child) => {
              newCheckedNodes.delete(child.id);
              const index = newSelectedMembers.findIndex(
                (m) => m.id === child.id,
              );
              if (
                index !== -1 &&
                !initialSelectedMembers.find((m) => m.id === child.id)
              ) {
                newSelectedMembers.splice(index, 1);
              }
              if (child.children) {
                removeChildNodes(child.children);
              }
            });
          };
          removeChildNodes(node.children);
        }
      } else {
        // 取消选中节点
        newCheckedNodes.delete(node.id);
        const index = newSelectedMembers.findIndex((m) => m.id === node.id);
        if (
          index !== -1 &&
          !initialSelectedMembers.find((m) => m.id === node.id)
        ) {
          newSelectedMembers.splice(index, 1);
        }
      }

      setCheckedNodes(newCheckedNodes);
      setSelectedMembers(newSelectedMembers);
    },
    [checkedNodes, selectedMembers, isNodeDisabled, initialSelectedMembers],
  );

  // 处理权限变更
  const handlePermissionChange = useCallback(
    (memberId: string, permission: PermissionType) => {
      setSelectedMembers((prev) =>
        prev.map((member) =>
          member.id === memberId ? { ...member, permission } : member,
        ),
      );
    },
    [],
  );

  // 处理移除成员
  const handleRemoveMember = useCallback(
    (memberId: string) => {
      // 不能移除外部传入的只读成员
      const member = selectedMembers.find((m) => m.id === memberId);
      if (member?.isReadonly) return;

      setSelectedMembers((prev) => prev.filter((m) => m.id !== memberId));
      setCheckedNodes((prev) => {
        const newSet = new Set(prev);
        newSet.delete(memberId);
        return newSet;
      });
    },
    [selectedMembers],
  );

  // 处理保存
  const handleSave = useCallback(() => {
    onConfirm?.(selectedMembers);
    onClose();
  }, [selectedMembers, onConfirm, onClose]);

  // 处理取消
  const handleCancel = useCallback(() => {
    // 重置为初始状态
    setSelectedMembers(initialSelectedMembers);
    setCheckedNodes(new Set(initialSelectedMembers.map((m) => m.id)));
    setSearchValue("");
    setCurrentPath(["root"]);
    onClose();
  }, [initialSelectedMembers, onClose]);

  return (
    <Modal
      title="添加指定成员"
      open={open}
      onCancel={handleCancel}
      width={900}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          保存
        </Button>,
      ]}
      className="permission-member-modal"
    >
      <div className="flex h-[500px] py-6">
        {/* 左侧：组织架构 */}
        <div className="flex w-1/2 flex-col border-r border-border-1 pr-3">
          <div className="mb-2 text-sm font-medium">组织架构</div>
          <Input
            placeholder="搜索组织或人员"
            prefix={<SearchIcon className="text-base text-text-3" />}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            allowClear
          />
          <div className="flex h-8 items-center gap-2 px-2">
            <TeamIcon className="text-base text-primary-default" />
            <Breadcrumb separator="/">
              {breadcrumbItems.map((item, index) => (
                <Breadcrumb.Item key={item.id}>
                  {index < breadcrumbItems.length - 1 ? (
                    <a
                      onClick={() => handleBreadcrumbClick(item.id)}
                      className="cursor-pointer text-blue-600 hover:text-blue-800"
                    >
                      {item.name}
                    </a>
                  ) : (
                    <span className="text-gray-500">{item.name}</span>
                  )}
                </Breadcrumb.Item>
              ))}
            </Breadcrumb>
          </div>
          <div className="flex-1 overflow-y-auto">
            {displayNodes.map((node) => {
              const isChecked = isNodeChecked(node.id);
              const isDisabled = isNodeDisabled(node.id);
              const hasChildren =
                node.type === "organization" &&
                node.children &&
                node.children.length > 0;

              return (
                <div
                  key={node.id}
                  className={cn(
                    "flex items-center gap-2 rounded px-2 py-[5px] hover:bg-gray-50",
                    isDisabled && "opacity-50",
                  )}
                >
                  <Checkbox
                    checked={isChecked}
                    disabled={isDisabled}
                    onChange={(e) => handleNodeCheck(node, e.target.checked)}
                  />
                  <div className="flex flex-1 items-center gap-2">
                    {node.type === "organization" ? (
                      <UserGroupIcon className="text-base text-primary-default" />
                    ) : (
                      <Avatar size={16} icon={<UserOutlined />} />
                    )}
                    <span
                      className={cn(
                        "flex-1 text-sm",
                        hasChildren &&
                          !searchValue.trim() &&
                          "cursor-pointer hover:text-blue-600",
                      )}
                      onClick={() =>
                        !searchValue.trim() && handleNodeClick(node)
                      }
                    >
                      {node.name}
                    </span>
                    {hasChildren && !searchValue.trim() && (
                      <RightArrowIcon
                        className="cursor-pointer text-xs text-text-2"
                        onClick={() => handleNodeClick(node)}
                      />
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
        {/* 右侧：已选择成员 */}
        <div className="flex w-1/2 flex-col pl-3">
          <div className="mb-2 text-sm font-medium">已选择成员</div>
          <div className="flex-1 overflow-y-auto">
            {selectedMembers.map((member) => (
              <div
                key={member.id}
                className="flex h-9 items-center gap-2 rounded px-2 hover:bg-gray-50"
              >
                {member.type === "organization" ? (
                  <UserGroupIcon className="text-base text-primary-default" />
                ) : (
                  <Avatar size={16} icon={<UserOutlined />} />
                )}
                <span className="flex-1 text-sm">{member.name}</span>
                <Select
                  value={member.permission}
                  onChange={(value: PermissionType | "remove") => {
                    if (value === "remove") {
                      handleRemoveMember(member.id);
                    } else {
                      handlePermissionChange(member.id, value);
                    }
                  }}
                  disabled={member.isReadonly}
                  size="small"
                  className="w-20"
                  options={[
                    ...PERMISSION_TYPE_OPTIONS,
                    {
                      label: "移除",
                      value: "remove",
                    },
                  ]}
                />
              </div>
            ))}
            {selectedMembers.length === 0 && (
              <div className="flex h-full flex-col items-center justify-center">
                <EmptyIcon className="mb-2 h-[120px] w-[120px]" />
                <span className="text-sm leading-[22px]">暂无指定人员</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
}
