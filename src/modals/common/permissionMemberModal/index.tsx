import { UserOutlined } from "@ant-design/icons";
import {
  Avatar,
  Breadcrumb,
  Button,
  Checkbox,
  Input,
  Modal,
  Select,
} from "antd";
import { useCallback, useEffect, useMemo, useState } from "react";

import EmptyIcon from "@/assets/Empty.svg?react";
import {
  RightArrowIcon,
  SearchIcon,
  TeamIcon,
  UserGroupIcon,
} from "@/components/main/icon";
import { useGetOrganizationMembers } from "@/controllers/API/queries/organization/useGetOrganizationMembers";
import useAuthStore from "@/stores/authStore";
import { OrganizationMember } from "@/types/organization";
import { cn } from "@/utils/utils";
import {
  PERMISSION_TYPE_OPTIONS,
  PermissionType,
} from "../resourcePermissionModal/helper";

// 已选择的成员或组织
interface SelectedMember {
  id: string;
  name: string;
  type: "user" | "department";
  avatar?: string;
  permission: PermissionType;
  isReadonly?: boolean; // 外部传入的不可编辑
}

interface PermissionMemberModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (selectedMembers: SelectedMember[]) => void;
  initialSelectedMembers?: SelectedMember[]; // 外部已选择的成员
}

// 面包屑项
interface BreadcrumbItem {
  id: string;
  name: string;
}

// 缓存的组织数据
interface CachedOrganizationData {
  members: OrganizationMember[];
  timestamp: number;
}

// 缓存时间（5分钟）
const CACHE_DURATION = 5 * 60 * 1000;

export default function PermissionMemberModal({
  open,
  onClose,
  onConfirm,
  initialSelectedMembers = [],
}: PermissionMemberModalProps) {
  const [searchValue, setSearchValue] = useState("");
  const [currentPath, setCurrentPath] = useState<BreadcrumbItem[]>([]);
  const [selectedMembers, setSelectedMembers] = useState<SelectedMember[]>(
    initialSelectedMembers,
  );
  const [checkedNodes, setCheckedNodes] = useState<Set<string>>(
    new Set(initialSelectedMembers.map((m) => m.id)),
  );
  const [currentOrganizationId, setCurrentOrganizationId] = useState<
    string | undefined
  >(undefined);

  // 缓存组织数据
  const [organizationCache, setOrganizationCache] = useState<
    Map<string, CachedOrganizationData>
  >(new Map());

  const userData = useAuthStore((state) => state.userData);

  // 获取当前组织的成员列表
  // 当 org_id 为空时，获取根组织列表
  const { data: organizationMembers, isLoading } = useGetOrganizationMembers(
    { org_id: currentOrganizationId },
    {
      enabled: open,
    },
  );

  console.log("organizationMembers", organizationMembers);

  // 初始化根组织信息
  useEffect(() => {
    if (open && currentPath.length === 0) {
      // 当弹窗打开且路径为空时，初始化为根组织
      // org_id 为 undefined 时获取根组织列表
      setCurrentOrganizationId(undefined);
      setCurrentPath([{ id: "root", name: userData?.current_tenant_name  }]);
    }
  }, [open, currentPath.length]);

  // 缓存数据更新
  useEffect(() => {
    if (organizationMembers && currentOrganizationId) {
      setOrganizationCache((prev) => {
        const newCache = new Map(prev);
        newCache.set(currentOrganizationId, {
          members: organizationMembers,
          timestamp: Date.now(),
        });
        return newCache;
      });
    }
  }, [organizationMembers, currentOrganizationId]);

  // 获取当前显示的成员列表
  const getCurrentMembers = useCallback((): OrganizationMember[] => {
    if (!currentOrganizationId) return [];

    // 先检查缓存
    const cached = organizationCache.get(currentOrganizationId);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.members;
    }

    // 返回当前请求的数据
    return organizationMembers || [];
  }, [currentOrganizationId, organizationCache, organizationMembers]);

  // 搜索功能
  const getFilteredMembers = useCallback((): OrganizationMember[] => {
    const members = getCurrentMembers();

    if (!searchValue.trim()) {
      return members;
    }

    return members.filter((member) =>
      member.name.toLowerCase().includes(searchValue.toLowerCase()),
    );
  }, [getCurrentMembers, searchValue]);

  // 获取显示的成员列表
  const displayMembers = useMemo(() => {
    return getFilteredMembers();
  }, [getFilteredMembers]);

  // 处理节点点击（进入子组织）
  const handleNodeClick = useCallback((member: OrganizationMember) => {
    if (member.type === "department" && member.has_children) {
      // 添加到路径
      setCurrentPath((prev) => [...prev, { id: member.id, name: member.name }]);
      setCurrentOrganizationId(member.id);
      setSearchValue(""); // 清空搜索
    }
  }, []);

  // 处理面包屑点击
  const handleBreadcrumbClick = useCallback(
    (targetId: string) => {
      const targetIndex = currentPath.findIndex((item) => item.id === targetId);
      if (targetIndex !== -1) {
        const newPath = currentPath.slice(0, targetIndex + 1);
        setCurrentPath(newPath);
        setCurrentOrganizationId(targetId);
        setSearchValue(""); // 清空搜索
      }
    },
    [currentPath],
  );

  // 检查节点是否被选中
  const isNodeChecked = useCallback(
    (nodeId: string) => {
      return checkedNodes.has(nodeId);
    },
    [checkedNodes],
  );

  // 检查节点是否不可编辑（外部传入的只读成员）
  const isNodeDisabled = useCallback(
    (nodeId: string) => {
      // 如果是外部传入的只读成员，不可编辑
      const initialMember = initialSelectedMembers.find((m) => m.id === nodeId);
      return initialMember?.isReadonly || false;
    },
    [initialSelectedMembers],
  );

  // 处理节点勾选
  const handleNodeCheck = useCallback(
    (member: OrganizationMember, checked: boolean) => {
      if (isNodeDisabled(member.id)) return;

      const newCheckedNodes = new Set(checkedNodes);
      const newSelectedMembers = [...selectedMembers];

      if (checked) {
        // 选中节点
        newCheckedNodes.add(member.id);

        // 添加到已选择列表
        if (!selectedMembers.find((m) => m.id === member.id)) {
          newSelectedMembers.push({
            id: member.id,
            name: member.name,
            type: member.type,
            permission: PermissionType.EDITABLE,
            isReadonly: false,
          });
        }
      } else {
        // 取消选中节点
        newCheckedNodes.delete(member.id);
        const index = newSelectedMembers.findIndex((m) => m.id === member.id);
        if (
          index !== -1 &&
          !initialSelectedMembers.find((m) => m.id === member.id)
        ) {
          newSelectedMembers.splice(index, 1);
        }
      }

      setCheckedNodes(newCheckedNodes);
      setSelectedMembers(newSelectedMembers);
    },
    [checkedNodes, selectedMembers, isNodeDisabled, initialSelectedMembers],
  );

  // 处理权限变更
  const handlePermissionChange = useCallback(
    (memberId: string, permission: PermissionType) => {
      setSelectedMembers((prev) =>
        prev.map((member) =>
          member.id === memberId ? { ...member, permission } : member,
        ),
      );
    },
    [],
  );

  // 处理移除成员
  const handleRemoveMember = useCallback(
    (memberId: string) => {
      // 不能移除外部传入的只读成员
      const member = selectedMembers.find((m) => m.id === memberId);
      if (member?.isReadonly) return;

      setSelectedMembers((prev) => prev.filter((m) => m.id !== memberId));
      setCheckedNodes((prev) => {
        const newSet = new Set(prev);
        newSet.delete(memberId);
        return newSet;
      });
    },
    [selectedMembers],
  );

  // 处理保存
  const handleSave = useCallback(() => {
    onConfirm?.(selectedMembers);
    onClose();
  }, [selectedMembers, onConfirm, onClose]);

  // 处理取消
  const handleCancel = useCallback(() => {
    // 重置为初始状态
    setSelectedMembers(initialSelectedMembers);
    setCheckedNodes(new Set(initialSelectedMembers.map((m) => m.id)));
    setSearchValue("");
    setCurrentPath([]);
    setCurrentOrganizationId(undefined);
    onClose();
  }, [initialSelectedMembers, onClose]);

  return (
    <Modal
      title="添加指定成员"
      open={open}
      onCancel={handleCancel}
      width={900}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          保存
        </Button>,
      ]}
      className="permission-member-modal"
    >
      <div className="flex h-[500px] py-6">
        {/* 左侧：组织架构 */}
        <div className="flex w-1/2 flex-col border-r border-border-1 pr-3">
          <div className="mb-2 text-sm font-medium">组织架构</div>
          <Input
            placeholder="搜索组织或人员"
            prefix={<SearchIcon className="text-base text-text-3" />}
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            allowClear
          />
          {/* 面包屑导航 */}
          {!searchValue.trim() && currentPath.length > 0 && (
            <div className="flex h-8 items-center gap-2 px-2">
              <TeamIcon className="text-base text-primary-default" />
              <Breadcrumb separator="/">
                {currentPath.map((item, index) => (
                  <Breadcrumb.Item key={item.id}>
                    {index < currentPath.length - 1 ? (
                      <a
                        onClick={() => handleBreadcrumbClick(item.id)}
                        className="cursor-pointer text-blue-600 hover:text-blue-800"
                      >
                        {item.name}
                      </a>
                    ) : (
                      <span className="text-gray-500">{item.name}</span>
                    )}
                  </Breadcrumb.Item>
                ))}
              </Breadcrumb>
            </div>
          )}
          {/* 组织架构列表 */}
          <div className="flex-1 overflow-y-auto">
            {isLoading ? (
              <div className="flex h-full items-center justify-center">
                <div className="text-sm text-gray-500">加载中...</div>
              </div>
            ) : displayMembers.length > 0 ? (
              displayMembers.map((member) => {
                const isChecked = isNodeChecked(member.id);
                const isDisabled = isNodeDisabled(member.id);
                const hasChildren =
                  member.type === "department" && member.has_children;

                return (
                  <div
                    key={member.id}
                    className={cn(
                      "flex items-center gap-2 rounded px-2 py-[5px] hover:bg-gray-50",
                      isDisabled && "opacity-50",
                    )}
                  >
                    <Checkbox
                      checked={isChecked}
                      disabled={isDisabled}
                      onChange={(e) =>
                        handleNodeCheck(member, e.target.checked)
                      }
                    />
                    <div className="flex flex-1 items-center gap-2">
                      {member.type === "department" ? (
                        <UserGroupIcon className="text-base text-primary-default" />
                      ) : (
                        <Avatar size={16} icon={<UserOutlined />} />
                      )}
                      <span
                        className={cn(
                          "flex-1 text-sm",
                          hasChildren &&
                            !searchValue.trim() &&
                            "cursor-pointer hover:text-blue-600",
                        )}
                        onClick={() =>
                          !searchValue.trim() &&
                          hasChildren &&
                          handleNodeClick(member)
                        }
                      >
                        {member.name}
                      </span>
                      {hasChildren && !searchValue.trim() && (
                        <RightArrowIcon
                          className="cursor-pointer text-xs text-text-2"
                          onClick={() => handleNodeClick(member)}
                        />
                      )}
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="flex h-full items-center justify-center">
                <div className="text-center text-gray-500">
                  <div className="text-sm">暂无数据</div>
                </div>
              </div>
            )}
          </div>
        </div>
        {/* 右侧：已选择成员 */}
        <div className="flex w-1/2 flex-col pl-3">
          <div className="mb-2 text-sm font-medium">已选择成员</div>
          <div className="flex-1 overflow-y-auto">
            {selectedMembers.map((member) => (
              <div
                key={member.id}
                className="flex h-9 items-center gap-2 rounded px-2 hover:bg-gray-50"
              >
                {member.type === "department" ? (
                  <UserGroupIcon className="text-base text-primary-default" />
                ) : (
                  <Avatar size={16} icon={<UserOutlined />} />
                )}
                <span className="flex-1 text-sm">{member.name}</span>
                <Select
                  value={member.permission}
                  onChange={(value: PermissionType | "remove") => {
                    if (value === "remove") {
                      handleRemoveMember(member.id);
                    } else {
                      handlePermissionChange(member.id, value);
                    }
                  }}
                  disabled={member.isReadonly}
                  size="small"
                  className="w-20"
                  options={[
                    ...PERMISSION_TYPE_OPTIONS,
                    {
                      label: "移除",
                      value: "remove",
                    },
                  ]}
                />
              </div>
            ))}
            {selectedMembers.length === 0 && (
              <div className="flex h-full flex-col items-center justify-center">
                <EmptyIcon className="mb-2 h-[120px] w-[120px]" />
                <span className="text-sm leading-[22px]">暂无指定人员</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
}
