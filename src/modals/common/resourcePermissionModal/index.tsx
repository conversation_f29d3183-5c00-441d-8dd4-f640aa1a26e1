import { Modal, Select, Tooltip } from "antd";
import { useEffect, useState } from "react";

import { QuestionIcon } from "@/components/main/icon";
import { cn } from "@/utils/utils";
import { PERMISSION_TYPE_OPTIONS, PermissionType } from "./helper";
import MemberList from "./memberList";

interface ResourcePermissionModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (permissionRange: string) => void;
}

enum PermissionRange {
  /** 只有我 */
  ONLY_ME = "ONLY_ME",
  /** 所在部门 */
  DEPARTMENT = "DEPARTMENT",
  /** 指定成员 */
  SPECIFIC_MEMBERS = "SPECIFIC_MEMBERS",
  /** 所有成员 */
  ALL_MEMBERS = "ALL_MEMBERS",
}

const rangeOptions = [
  {
    id: PermissionRange.ONLY_ME,
    label: "只有我",
    tooltip: "只有创建者可以访问",
  },
  {
    id: PermissionRange.DEPARTMENT,
    label: "所在部门",
    tooltip: "所在部门的所有成员均可访问。",
  },
  {
    id: PermissionRange.SPECIFIC_MEMBERS,
    label: "指定成员",
    tooltip: "指定特定成员可以访问",
  },
  {
    id: PermissionRange.ALL_MEMBERS,
    label: "所有成员",
    tooltip: "团队所有成员均可访问",
  },
];

const mockData = [
  {
    id: "1",
    username: "张三",
    role: "所有者",
    icon: "A",
  },
  {
    id: "2",
    username: "李四",
    role: "管理员",
    icon: "L",
  },
  {
    id: "3",
    username: "王五",
    role: "成员",
    icon: "W",
  },
];

export default function ResourcePermissionModal({
  open,
  onClose,
  onConfirm,
}: ResourcePermissionModalProps) {
  const [permissionRange, setPermissionRange] = useState<PermissionRange>(
    PermissionRange.ONLY_ME,
  );
  const [permissionType, setPermissionType] = useState<PermissionType>(
    PermissionType.EDITABLE,
  );

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  const handleRangeSelect = (range: PermissionRange) => {
    setPermissionRange(range);
  };

  const handleTypeSelect = (type: PermissionType) => {
    setPermissionType(type);
  };

  const handleConfirm = () => {
    onConfirm?.(permissionRange);
    onClose();
  };

  const reset = () => {
    setPermissionRange(PermissionRange.ONLY_ME);
    setPermissionType(PermissionType.EDITABLE);
  };

  const renderContent = () => {
    if (
      [PermissionRange.DEPARTMENT, PermissionRange.ALL_MEMBERS].includes(
        permissionRange,
      )
    ) {
      return (
        <div className="mt-3">
          <div className="mb-1 font-medium">权限设置</div>
          <Select
            className="w-full"
            value={permissionType}
            onChange={handleTypeSelect}
            options={PERMISSION_TYPE_OPTIONS}
          />
        </div>
      );
    }
    if (permissionRange === PermissionRange.SPECIFIC_MEMBERS) {
      return <MemberList data={mockData} />;
    }

    return null;
  };

  return (
    <Modal
      title="权限管理"
      open={open}
      onCancel={onClose}
      onOk={handleConfirm}
      width={500}
      okText="确定"
      cancelText="取消"
      footer={null}
    >
      <h3 className="mb-3 mt-6 text-sm font-medium">可见范围</h3>
      <div className="grid grid-cols-2 gap-3">
        {rangeOptions.map((option) => (
          <div
            key={option.id}
            className={cn(
              "relative flex h-[54px] cursor-pointer items-center rounded-lg border p-4 transition-all duration-200",
              permissionRange === option.id
                ? "border-primary-default bg-bg-primary-1"
                : "border-border-1 bg-white hover:bg-bg-light-3",
            )}
            onClick={() => handleRangeSelect(option.id)}
          >
            <div className="flex w-full items-center justify-between">
              <span className="text-sm font-medium text-gray-900">
                {option.label}
              </span>
              <Tooltip title={option.tooltip}>
                <QuestionIcon className="text-base text-text-3" />
              </Tooltip>
            </div>
          </div>
        ))}
      </div>
      {renderContent()}
    </Modal>
  );
}
