import { Modal, Select, Tooltip } from "antd";
import { useEffect, useState } from "react";

import { QuestionIcon } from "@/components/main/icon";
import { useGetResourcePermission } from "@/controllers/API/queries/permission/useGetResourcePermission";
import { useUpdateResourcePermission } from "@/controllers/API/queries/permission/useUpdateResourcePermission";
import useAuthStore from "@/stores/authStore";
import { PermissionItem } from "@/types/permission";
import { cn } from "@/utils/utils";
import { PERMISSION_TYPE_OPTIONS, PermissionType } from "./helper";
import MemberList from "./memberList";

interface ResourcePermissionModalProps {
  open: boolean;
  resourceType: "app" | "knowledgebase" | "workflow";
  resourceId: string;
  onClose: () => void;
  onConfirm?: () => void;
}

enum PermissionScope {
  /** 只有我 */
  OWNER_ONLY = "owner_only",
  /** 所在部门 */
  ORGANIZATION = "organization",
  /** 指定成员 */
  MEMBERS = "members",
  /** 所有成员 */
  TENANT_ALL = "tenant_all",
}

const rangeOptions = [
  {
    id: PermissionScope.OWNER_ONLY,
    label: "只有我",
    tooltip: "只有创建者可以访问",
  },
  {
    id: PermissionScope.ORGANIZATION,
    label: "所在部门",
    tooltip: "所在部门的所有成员均可访问。",
  },
  {
    id: PermissionScope.MEMBERS,
    label: "指定成员",
    tooltip: "指定特定成员可以访问",
  },
  {
    id: PermissionScope.TENANT_ALL,
    label: "所有成员",
    tooltip: "团队所有成员均可访问",
  },
];

const mockData = [
  {
    id: "1",
    username: "张三",
    role: "所有者",
    icon: "A",
  },
  {
    id: "2",
    username: "李四",
    role: "管理员",
    icon: "L",
  },
  {
    id: "3",
    username: "王五",
    role: "成员",
    icon: "W",
  },
];

export default function ResourcePermissionModal({
  open,
  resourceType,
  resourceId,
  onClose,
  onConfirm,
}: ResourcePermissionModalProps) {
  const [permissionScope, setPermissionScope] = useState<
    PermissionScope | undefined
  >();
  const [permissionType, setPermissionType] = useState<PermissionType>(
    PermissionType.EDITABLE,
  );

  const userData = useAuthStore((state) => state.userData);

  const { data: permissionData } = useGetResourcePermission(
    { resource_type: resourceType, resource_id: resourceId },
    { enabled: open },
  );

  const updatePermission = useUpdateResourcePermission();

  useEffect(() => {
    const scope = permissionData?.resource?.permission_scope;
    setPermissionScope(scope);

    if (
      permissionData?.resource?.permission_scope ===
        PermissionScope.ORGANIZATION ||
      permissionData?.resource?.permission_scope === PermissionScope.TENANT_ALL
    ) {
      const permissionType = permissionData?.permissions?.find(
        (item) =>
          item.subject_type === "organization" ||
          item.subject_type === "tenant",
      )?.permission_type;
      setPermissionType(permissionType);
    }
  }, [permissionData]);

  useEffect(() => {
    if (!open) {
      reset();
    }
  }, [open]);

  const handleRangeSelect = (range: PermissionScope) => {
    setPermissionScope(range);
  };

  const handleTypeSelect = (type: PermissionType) => {
    setPermissionType(type);
  };

  const handleSubmit = async () => {
    if (!permissionScope || !userData?.id) return;

    let permissions: PermissionItem[] = [];

    if (permissionScope === PermissionScope.OWNER_ONLY) {
      permissions = [
        {
          subject_type: "user",
          subject_id: userData?.id,
          permission_type: permissionType,
          expire_at: null,
        },
      ];
    }
    if (permissionScope === PermissionScope.ORGANIZATION) {
      permissions = [
        {
          subject_type: "organization",
          subject_id: userData?.current_organization_id,
          permission_type: permissionType,
          expire_at: null,
        },
      ];
    }
    if (permissionScope === PermissionScope.TENANT_ALL) {
      permissions = [
        {
          subject_type: "tenant",
          subject_id: userData?.current_tenant_id,
          permission_type: permissionType,
          expire_at: null,
        },
      ];
    }

    await updatePermission.mutateAsync({
      resource_type: resourceType,
      resource_id: resourceId,
      permissions,
      permission_scope: permissionScope,
    });

    onConfirm?.();
    onClose();
  };

  const reset = () => {
    setPermissionScope(PermissionScope.OWNER_ONLY);
    setPermissionType(PermissionType.EDITABLE);
  };

  const renderContent = () => {
    if (
      permissionScope &&
      [PermissionScope.ORGANIZATION, PermissionScope.TENANT_ALL].includes(
        permissionScope,
      )
    ) {
      return (
        <div className="mt-3">
          <div className="mb-1 font-medium">权限设置</div>
          <Select
            className="w-full"
            value={permissionType}
            onChange={handleTypeSelect}
            options={PERMISSION_TYPE_OPTIONS}
          />
        </div>
      );
    }
    if (permissionScope === PermissionScope.MEMBERS) {
      return <MemberList data={mockData} />;
    }

    return null;
  };

  return (
    <Modal
      title="权限管理"
      open={open}
      onCancel={onClose}
      onOk={handleSubmit}
      width={500}
      okText="确定"
      cancelText="取消"
      confirmLoading={updatePermission.isPending}
    >
      <h3 className="mb-3 mt-6 text-sm font-medium">可见范围</h3>
      <div className="grid grid-cols-2 gap-3">
        {rangeOptions.map((option) => (
          <div
            key={option.id}
            className={cn(
              "relative flex h-[54px] cursor-pointer items-center rounded-lg border p-4 transition-all duration-200",
              permissionScope === option.id
                ? "border-primary-default bg-bg-primary-1"
                : "border-border-1 bg-white hover:bg-bg-light-3",
            )}
            onClick={() => handleRangeSelect(option.id)}
          >
            <div className="flex w-full items-center justify-between">
              <span className="text-sm font-medium text-gray-900">
                {option.label}
              </span>
              <Tooltip title={option.tooltip}>
                <QuestionIcon className="text-base text-text-3" />
              </Tooltip>
            </div>
          </div>
        ))}
      </div>
      {renderContent()}
    </Modal>
  );
}
