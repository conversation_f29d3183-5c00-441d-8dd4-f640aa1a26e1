import { Avatar, Button, Divider, Modal, Select } from "antd";
import { useState, useContext } from "react";

import { Badge, BadgeVariant } from "@/components/main/badge";
import { SquarePlusIcon, WarningIcon } from "@/components/main/icon";
import { AuthContext } from "@/contexts/authContext";
import PermissionMemberModal from "../permissionMemberModal";
import { PERMISSION_TYPE_OPTIONS, PermissionType } from "./helper";

interface MemberListProps {
  data: SelectedMember[];
}

interface SelectedMember {
  id: string;
  username: string;
  role: string;
  icon: string;
}

export default function MemberList({ data }: MemberListProps) {
  const [isAddMemberModalOpen, setIsAddMemberModalOpen] = useState(false);
  const { userData } = useContext(AuthContext);

  const handleRemove = () => {
    Modal.confirm({
      title: "确定移除该成员吗？",
      content:
        "移除后，该成员将无法访问当前内容或相关权限配置，且需重新邀请后才能恢复访问。",
      okText: "移除",
      cancelText: "暂不",
      icon: (
        <WarningIcon className="mr-3 flex-shrink-0 text-2xl text-[#FBB310]" />
      ),
    });
  };

  const handleAddMember = () => {
    setIsAddMemberModalOpen(true);
  };

  const handleAddMemberConfirm = (
    selectedMembers: Array<{
      id: string;
      name: string;
      type: "user" | "department";
      permission: PermissionType;
      isReadonly?: boolean;
    }>,
  ) => {
    console.log("新增成员:", selectedMembers);
    // 这里可以调用父组件的回调函数来更新成员列表
  };

  const handleAddMemberClose = () => {
    setIsAddMemberModalOpen(false);
  };

  return (
    <div className="mt-3">
      <div className="flex gap-2">
        <div className="font-medium">已选择成员</div>
        <Button
          size="small"
          className="gap-0.5 text-xs"
          onClick={handleAddMember}
        >
          <SquarePlusIcon className="text-sm" />
          添加成员
        </Button>
      </div>
      <div>
        {data.map((item) => {
          return (
            <div
              key={item.id}
              className="mt-2 flex h-[28px] items-center justify-between"
            >
              <div className="flex flex-1 items-center gap-2">
                <Avatar
                  className="bg-bg-green-1 text-[8px] font-medium text-primary-default"
                  size={16}
                >
                  A
                </Avatar>
                <div>{item.username}</div>
                <Badge variant={BadgeVariant.PROCESSING}>所有者</Badge>
              </div>
              <Select
                className="w-[90px] flex-shrink-0"
                options={PERMISSION_TYPE_OPTIONS}
                popupRender={(menu) => {
                  return (
                    <>
                      {menu}
                      <Divider className="my-1" />
                      <div
                        className="flex h-8 cursor-pointer items-center rounded-md px-3"
                        onClick={handleRemove}
                      >
                        移除
                      </div>
                    </>
                  );
                }}
              />
            </div>
          );
        })}
      </div>
      <PermissionMemberModal
        open={isAddMemberModalOpen}
        onClose={handleAddMemberClose}
        onConfirm={handleAddMemberConfirm}
        rootOrganizationId={userData?.current_organization_id}
        initialSelectedMembers={data.map(member => ({
          id: member.id,
          name: member.username,
          type: 'user' as const,
          permission: PermissionType.EDITABLE,
          isReadonly: true // 现有成员设为只读
        }))}
      />
    </div>
  );
}
