import { <PERSON><PERSON>, <PERSON><PERSON>, Di<PERSON><PERSON>, <PERSON><PERSON>, Select } from "antd";

import { <PERSON><PERSON>, BadgeVariant } from "@/components/main/badge";
import { SquarePlusIcon, WarningIcon } from "@/components/main/icon";
import { PERMISSION_TYPE_OPTIONS } from "./helper";

interface MemberListProps {
  data: SelectedMember[];
}

interface SelectedMember {
  id: string;
  username: string;
  role: string;
  icon: string;
}

export default function MemberList({ data }: MemberListProps) {
  const handleRemove = () => {
    Modal.confirm({
      title: "确定移除该成员吗？",
      content:
        "移除后，该成员将无法访问当前内容或相关权限配置，且需重新邀请后才能恢复访问。",
      okText: "移除",
      cancelText: "暂不",
      icon: (
        <WarningIcon className="mr-3 flex-shrink-0 text-2xl text-[#FBB310]" />
      ),
    });
  };

  return (
    <div className="mt-3">
      <div className="flex gap-2">
        <div className="font-medium">已选择成员</div>
        <Button size="small" className="gap-0.5 text-xs">
          <SquarePlusIcon className="text-sm" />
          添加成员
        </Button>
      </div>
      <div>
        {data.map((item) => {
          return (
            <div
              key={item.id}
              className="mt-2 flex h-[28px] items-center justify-between"
            >
              <div className="flex flex-1 items-center gap-2">
                <Avatar
                  className="bg-bg-green-1 text-[8px] font-medium text-primary-default"
                  size={16}
                >
                  A
                </Avatar>
                <div>{item.username}</div>
                <Badge variant={BadgeVariant.PROCESSING}>所有者</Badge>
              </div>
              <Select
                className="w-[90px] flex-shrink-0"
                options={PERMISSION_TYPE_OPTIONS}
                popupRender={(menu) => {
                  return (
                    <>
                      {menu}
                      <Divider className="my-1" />
                      <div
                        className="flex h-8 cursor-pointer items-center rounded-md px-3"
                        onClick={handleRemove}
                      >
                        移除
                      </div>
                    </>
                  );
                }}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
}
