import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  user_id: string;
}

export const useDeleteUser: useMutationFunctionType<
  undefined,
  RequestParams,
  string
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const deleteUserFn = async (payload: RequestParams): Promise<string> => {
    const url = getURL(
      "DELETE_USER",
      { user_id: payload.user_id },
      false,
      true,
    );
    const res = await api.delete<ResponseType<string>>(url);
    return res.data?.data;
  };

  const mutation = mutate(["useDeleteUser"], deleteUserFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetOrganizationUsers"],
      });
    },
  });

  return mutation;
};
