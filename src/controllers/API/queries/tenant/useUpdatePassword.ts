import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import type { OrganizationUser } from "@/types/organization";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  user_id: string;
  new_password: string;
}

export const useUpdatePassword: useMutationFunctionType<
  undefined,
  RequestParams,
  OrganizationUser
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const updatePasswordFn = async (
    payload: RequestParams,
  ): Promise<OrganizationUser> => {
    const url = `${getURL("UPDATE_PASSWORD", { user_id: payload.user_id }, false, true)}/password`;
    const res = await api.put<ResponseType<OrganizationUser>>(url, {
      new_password: payload.new_password,
    });
    return res.data?.data;
  };

  const mutation = mutate(["useUpdatePassword"], updatePasswordFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetOrganizationUsers"],
      });
    },
  });

  return mutation;
};
