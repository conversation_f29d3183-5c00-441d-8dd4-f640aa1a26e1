import { useQueryFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  resource_type: "app" | "knowledgebase" | "workflow";
  resource_id: string;
}

type ReturnData = any;

export const useGetResourcePermission: useQueryFunctionType<
  RequestParams,
  ReturnData | undefined
> = (params, options) => {
  const { query } = UseRequestProcessor();

  const getOrganUsersFn = async (
    params: RequestParams,
  ): Promise<ReturnData | undefined> => {
    const url = `${getURL("GET_RESOURCE_PERMISSION", {}, false, true)}/${params.resource_type}/${params.resource_id}/permissions`;
    const res = await api.get<ResponseType<ReturnData>>(url);
    return res.data?.data;
  };

  const queryResult = query(
    [
      "useGetResourcePermission",
      {
        resource_type: params.resource_type,
        resource_id: params.resource_id,
      },
    ],
    () => getOrganUsersFn(params),
    {
      refetchOnWindowFocus: false,
      ...options,
    },
  );

  return queryResult;
};
